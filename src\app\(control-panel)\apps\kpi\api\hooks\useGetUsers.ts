import { useQuery } from '@tanstack/react-query';
import { databaseService } from '@/services/databaseService';

/**
 * Hook to get all users from database
 */
export function useGetUsers() {
	return useQuery({
		queryKey: ['kpi', 'users'],
		queryFn: async () => {
			const users = await databaseService.getUsers();
			return users;
		},
		staleTime: 30000, // Consider data stale after 30 seconds
		refetchInterval: 60000, // Refetch every minute
	});
}