/**
 * Database configuration for MySQL connection
 */
export const databaseConfig = {
	development: {
		host: '**************', // Servidor remoto para desarrollo
		port: 3306,
		user: 'ncornejo',
		password: 'N1c0l7as17#',
		database: 'renovatio'
	},
	production: {
		host: import.meta.env.VITE_DB_HOST || 'localhost', // localhost en VPS
		port: Number(import.meta.env.VITE_DB_PORT) || 3306,
		user: import.meta.env.VITE_DB_USER || 'ncornejo',
		password: import.meta.env.VITE_DB_PASSWORD || 'N1c0l7as17#',
		database: import.meta.env.VITE_DB_NAME || 'renovatio'
	}
};

export const getCurrentDatabaseConfig = () => {
	const isDevelopment = import.meta.env.DEV;
	return isDevelopment ? databaseConfig.development : databaseConfig.production;
};

export default databaseConfig;
