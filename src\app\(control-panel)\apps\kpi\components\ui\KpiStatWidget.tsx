import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { ReactNode } from 'react';

interface KpiStatWidgetProps {
	title: string;
	value: string | number;
	icon: ReactNode;
	color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
	description?: string;
	trend?: {
		value: number;
		isPositive: boolean;
	};
}

function KpiStatWidget({ 
	title, 
	value, 
	icon, 
	color = 'primary', 
	description,
	trend 
}: KpiStatWidgetProps) {
	const colorClasses = {
		primary: 'bg-blue-50 text-blue-600',
		secondary: 'bg-purple-50 text-purple-600',
		error: 'bg-red-50 text-red-600',
		warning: 'bg-orange-50 text-orange-600',
		info: 'bg-cyan-50 text-cyan-600',
		success: 'bg-green-50 text-green-600',
	};

	return (
		<Paper className="p-24 relative overflow-hidden">
			<Box className="flex items-start justify-between">
				<Box className="flex-1">
					<Typography 
						variant="caption" 
						className="text-gray-600 uppercase font-medium tracking-wider"
					>
						{title}
					</Typography>
					<Typography 
						variant="h4" 
						className="font-bold mt-8 mb-4"
					>
						{value}
					</Typography>
					{description && (
						<Typography 
							variant="body2" 
							color="text.secondary"
							className="mt-4"
						>
							{description}
						</Typography>
					)}
					{trend && (
						<Box className="flex items-center gap-4 mt-8">
							<Typography 
								variant="caption" 
								className={trend.isPositive ? 'text-green-600' : 'text-red-600'}
							>
								{trend.isPositive ? '↑' : '↓'} {Math.abs(trend.value)}%
							</Typography>
							<Typography variant="caption" color="text.secondary">
								vs. mes anterior
							</Typography>
						</Box>
					)}
				</Box>
				<Box className={`p-12 rounded-lg ${colorClasses[color]}`}>
					{icon}
				</Box>
			</Box>
		</Paper>
	);
}

export default KpiStatWidget;