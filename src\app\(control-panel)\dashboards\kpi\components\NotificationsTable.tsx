'use client';
import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Box,
  TablePagination,
  TextField,
  InputAdornment,
  Alert,
  CircularProgress
} from '@mui/material';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { notificationsService, Notification } from '../api/services/notificationsService';



export default function NotificationsTable() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [totalCount, setTotalCount] = useState(0);

  // Cargar notificaciones al montar el componente
  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('📊 Cargando notificaciones desde MySQL...');
      
      // Obtener notificaciones directamente
      const data = await notificationsService.getAllNotifications(2);
      const count = await notificationsService.getNotificationsCount(2);
      
      setNotifications(data);
      setTotalCount(count);
      
      console.log(`✅ Cargadas ${data.length} notificaciones`);
    } catch (error) {
      console.error('❌ Error al cargar notificaciones:', error);
      setError('Error al cargar las notificaciones desde la base de datos');
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (notificationId: number) => {
    try {
      const success = await notificationsService.markAsRead(notificationId);
      if (success) {
        // Actualizar el estado local
        setNotifications(prev => 
          prev.map(n => 
            n.id === notificationId 
              ? { ...n, read_status: true }
              : n
          )
        );
        console.log(`✅ Notificación ${notificationId} marcada como leída`);
      }
    } catch (error) {
      console.error('❌ Error al marcar como leída:', error);
    }
  };

  // Filtrar notificaciones según término de búsqueda
  const filteredNotifications = notifications.filter(notification =>
    notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    notification.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    notification.variant.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Paginación
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Obtener notificaciones para la página actual
  const paginatedNotifications = filteredNotifications.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const getVariantColor = (variant: string) => {
    switch (variant) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'warning': return 'warning';
      case 'info': return 'info';
      default: return 'default';
    };
  };

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm:ss', { locale: es });
  };

  if (loading) {
    return (
      <>
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <CircularProgress className="mr-3" />
            <Typography>Cargando notificaciones desde MySQL...</Typography>
          </CardContent>
        </Card>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Card>
          <CardContent>
            <Alert severity="error" className="mb-4">
              {error}
            </Alert>
            <Box className="text-center">
              <button 
                onClick={loadNotifications}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Reintentar
              </button>
            </Box>
          </CardContent>
        </Card>
      </>
    );
  }

  return (
    <>
      {/* Componente de prueba de conexión */}
      
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6" component="h2">
              📋 Tabla de Notificaciones - Base de Datos MySQL
            </Typography>
            <Box className="flex gap-2">
              <Chip 
                label={`${filteredNotifications.length} registros`} 
                color="primary" 
              />
              <Chip 
                label={`${notifications.filter(n => !n.read_status).length} no leídas`} 
                color="warning" 
                variant="outlined"
              />
            </Box>
          </Box>

          {/* Información de conexión */}
          <Alert severity="info" className="mb-4">
            <Typography variant="body2">
              🔗 <strong>CONEXIÓN DIRECTA A MYSQL:</strong> 
              Servidor: <strong>**************:3306</strong> | 
              Base de datos: <strong>renovatio</strong> | 
              Usuario: <strong>ncornejo</strong> | 
              Tabla: <strong>notifications</strong>
            </Typography>
          </Alert>

        {/* Buscador */}
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Buscar notificaciones..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ mb: 3 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <span className="material-icons">search</span>
              </InputAdornment>
            ),
          }}
        />

        {/* Tabla de notificaciones */}
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: 'action.hover' }}>
                <TableCell><strong>ID</strong></TableCell>
                <TableCell><strong>Usuario ID</strong></TableCell>
                <TableCell><strong>Título</strong></TableCell>
                <TableCell><strong>Descripción</strong></TableCell>
                <TableCell><strong>Icono</strong></TableCell>
                <TableCell><strong>Variante</strong></TableCell>
                <TableCell><strong>Estado</strong></TableCell>
                <TableCell><strong>Fecha y Hora</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedNotifications.map((notification) => (
                <TableRow
                  key={notification.id}
                  sx={{ 
                    '&:hover': { backgroundColor: 'action.hover' },
                    opacity: notification.read_status ? 0.7 : 1
                  }}
                >
                  <TableCell>{notification.id}</TableCell>
                  <TableCell>{notification.user_id}</TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight={notification.read_status ? 'normal' : 'bold'}>
                      {notification.title}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {notification.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box className="flex items-center gap-2">
                      <span className="material-icons" style={{ fontSize: 20 }}>
                        {notification.icon.replace('lucide:', '')}
                      </span>
                      <Typography variant="caption" color="text.secondary">
                        {notification.icon}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={notification.variant}
                      size="small"
                      color={getVariantColor(notification.variant) as any}
                    />
                  </TableCell>
                  <TableCell>
                    <Box className="flex items-center gap-2">
                      <Chip
                        label={notification.read_status ? 'Leído' : 'No leído'}
                        size="small"
                        color={notification.read_status ? 'default' : 'primary'}
                        variant={notification.read_status ? 'outlined' : 'filled'}
                      />
                      {!notification.read_status && (
                        <IconButton 
                          size="small" 
                          onClick={() => handleMarkAsRead(notification.id)}
                          title="Marcar como leído"
                        >
                          <span className="material-icons" style={{ fontSize: 16 }}>done</span>
                        </IconButton>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="caption">
                      {formatDateTime(notification.time)}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Paginación */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredNotifications.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Filas por página:"
          labelDisplayedRows={({ from, to, count }) => 
            `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`
          }
        />

        {/* Resumen */}
        <Box mt={3} p={2} bgcolor="background.default" borderRadius={1}>
          <Typography variant="body2" color="text.secondary">
            <strong>Resumen:</strong> {totalCount} notificaciones totales | 
            {notifications.filter(n => !n.read_status).length} no leídas | 
            Mostrando página {page + 1} de {Math.ceil(filteredNotifications.length / rowsPerPage)} |
            <strong> 📊 Datos desde MySQL VPS</strong>
          </Typography>
        </Box>
      </CardContent>
    </Card>
  </>
  );
}