import { databaseService } from '@/services/databaseService';

export interface Notification {
  id: number;
  user_id: number;
  title: string;
  description: string;
  icon: string;
  time: string;
  read_status: boolean;
  variant: string;
}

// Función para convertir el formato de MySQL al formato de la aplicación
function mapNotificationFromDB(row: any): Notification {
  return {
    id: row.id,
    user_id: row.user_id,
    title: row.title,
    description: row.description,
    icon: row.icon,
    time: row.created_at,
    read_status: <PERSON><PERSON><PERSON>(row.read_status),
    variant: row.variant
  };
}

export const notificationsService = {
  /**
   * Obtiene todas las notificaciones de MySQL (versión simplificada)
   */
  getAllNotifications: async (userId?: number): Promise<Notification[]> => {
    try {
      console.log('📊 Obteniendo notificaciones desde MySQL (simple)...');
      
      const dbNotifications = await databaseService.query('SELECT * FROM notifications WHERE user_id = ?', [userId]);
      const notifications = dbNotifications.map(mapNotificationFromDB);
      
      console.log(`✅ Procesadas ${notifications.length} notificaciones`);
      return notifications;
      
    } catch (error) {
      console.error('❌ Error en getAllNotifications:', error);
      return [];
    }
  },

  /**
   * Marca una notificación como leída (simulado por ahora)
   */
  markAsRead: async (notificationId: number): Promise<boolean> => {
    try {
      console.log(`📝 Marcando notificación ${notificationId} como leída...`);
      // TODO: Implementar endpoint para marcar como leída
      console.log('⚠️ Función markAsRead pendiente de implementar en backend');
      return true;
    } catch (error) {
      console.error('❌ Error en markAsRead:', error);
      return false;
    }
  },

  /**
   * Obtiene el conteo total de notificaciones
   */
  getNotificationsCount: async (userId?: number): Promise<number> => {
    try {
      const notifications = await notificationsService.getAllNotifications(userId);
      return notifications.length;
    } catch (error) {
      console.error('❌ Error en getNotificationsCount:', error);
      return 0;
    }
  },

  /**
   * Prueba la conexión al backend MySQL
   */
  testConnection: async (): Promise<boolean> => {
    return await databaseService.testConnection();
  }
};