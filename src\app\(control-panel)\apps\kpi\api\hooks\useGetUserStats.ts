import { useQuery } from '@tanstack/react-query';
import { databaseService } from '@/services/databaseService';

/**
 * Hook to get user statistics from database
 */
export function useGetUserStats() {
	return useQuery({
		queryKey: ['kpi', 'userStats'],
		queryFn: async () => {
			const stats = await databaseService.getUserStats();
			return stats;
		},
		staleTime: 30000, // Consider data stale after 30 seconds
		refetchInterval: 60000, // Refetch every minute
	});
}